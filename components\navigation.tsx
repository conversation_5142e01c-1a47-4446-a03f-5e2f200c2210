"use client";

import { handleSmoothScroll } from "@/lib/smoothScroll";
import { useLenis } from "lenis/react";
import { motion, useMotionValueEvent, useScroll } from "motion/react";
import Link from "next/link";
import { useState } from "react";
import TextRoll from "./animations/text-roll";
import MobileMenu from "./mobile-menu";
import { useLoading } from "./providers/loading-provider";

const navItems = [
   {
      label: "About",
      href: "/#about",
   },
   {
      label: "What I do",
      href: "/#what-i-do",
   },
   {
      label: "Services",
      href: "/#services",
   },
   {
      label: "Trusted Brands",
      href: "/#brands",
   },
   {
      label: "Contact Me",
      href: "/#contact",
   },
];

export default function Navigation() {
   const { isLoadingComplete } = useLoading();
   const lenis = useLenis();

   // Scroll-based visibility state
   const [isVisible, setIsVisible] = useState(true);
   const [lastScrollY, setLastScrollY] = useState(0);
   const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

   // Track scroll position and direction
   const { scrollY } = useScroll();

   useMotionValueEvent(scrollY, "change", (latest) => {
      const currentScrollY = latest;
      const viewportHeight = window.innerHeight;

      // Check if we're on mobile (screen width < 768px for md breakpoint)
      const isMobile = window.innerWidth < 768;

      // Skip auto-hide behavior when mobile menu is open
      if (isMobile && isMobileMenuOpen) {
         // Always keep navigation visible when mobile menu is open
         if (!isVisible) {
            setIsVisible(true);
         }
         setLastScrollY(currentScrollY);
         return;
      }

      // Check if we've scrolled past 100vh
      const pastThreshold = currentScrollY > viewportHeight;

      // Only apply hide/show logic after scrolling past 100vh
      if (pastThreshold) {
         const scrollDirection = currentScrollY > lastScrollY ? "down" : "up";

         // Hide when scrolling down, show when scrolling up
         if (scrollDirection === "down" && isVisible) {
            setIsVisible(false);
         } else if (scrollDirection === "up" && !isVisible) {
            setIsVisible(true);
         }
      } else {
         // Always show navigation before 100vh threshold
         if (!isVisible) {
            setIsVisible(true);
         }
      }

      setLastScrollY(currentScrollY);
   });

   return (
      <>
         {/* Mobile Menu */}
         <MobileMenu
            isVisible={isLoadingComplete && isVisible}
            onMenuStateChange={setIsMobileMenuOpen}
         />

         {/* Desktop Navigation */}
         <motion.div
            className="bg-background sticky top-0 z-100 flex h-[80px] flex-row items-center justify-between px-5 py-5 pr-8 text-base font-medium tracking-wider"
            initial={{ y: -100, opacity: 0 }}
            animate={{
               y: !isLoadingComplete ? -100 : isVisible ? 0 : -100,
               opacity: !isLoadingComplete ? 0 : isVisible ? 1 : 0,
            }}
            transition={{
               duration: isLoadingComplete ? 0.4 : 0.8,
               delay: isLoadingComplete ? 0 : 0.2,
               ease: [0.25, 0.46, 0.45, 0.94],
            }}
         >
            <motion.div
               initial={{ y: -50, opacity: 0 }}
               animate={
                  isLoadingComplete
                     ? { y: 0, opacity: 1 }
                     : { y: -50, opacity: 0 }
               }
               transition={{
                  duration: 0.6,
                  delay: isLoadingComplete ? 0.4 : 0,
                  ease: [0.25, 0.46, 0.45, 0.94],
               }}
            >
               <Link
                  href="/"
                  className="text-primary font-serif text-2xl font-thin italic"
               >
                  Beckizedek
               </Link>
            </motion.div>
            {/* Desktop Navigation Items - Hidden on mobile */}
            <motion.div
               className="hidden flex-row gap-12 md:flex"
               initial={{ y: -50, opacity: 0 }}
               animate={
                  isLoadingComplete
                     ? { y: 0, opacity: 1 }
                     : { y: -50, opacity: 0 }
               }
               transition={{
                  duration: 0.6,
                  delay: isLoadingComplete ? 0.6 : 0,
                  ease: [0.25, 0.46, 0.45, 0.94],
               }}
            >
               {navItems.map((item, index) => (
                  <motion.div
                     key={item.label}
                     initial={{ y: -30, opacity: 0 }}
                     animate={
                        isLoadingComplete
                           ? { y: 0, opacity: 1 }
                           : { y: -30, opacity: 0 }
                     }
                     transition={{
                        duration: 0.5,
                        delay: isLoadingComplete ? 0.8 + index * 0.1 : 0,
                        ease: [0.25, 0.46, 0.45, 0.94],
                     }}
                  >
                     <Link
                        href={item.href}
                        onClick={(e) => handleSmoothScroll(e, item.href, lenis)}
                        className="font-gilroy text-primary text-sm font-bold tracking-wide uppercase opacity-70 transition-all hover:opacity-100"
                     >
                        <TextRoll center>{item.label}</TextRoll>
                     </Link>
                  </motion.div>
               ))}
            </motion.div>
         </motion.div>
      </>
   );
}
