"use client";

import AboutSection from "@/components/pages/home/<USER>";
import BrandsSection from "@/components/pages/home/<USER>";
import CtaSection from "@/components/pages/home/<USER>";
import HeroSection from "@/components/pages/home/<USER>";
import IntroSection from "@/components/pages/home/<USER>";
import ServicesSection from "@/components/pages/home/<USER>";
import ValueSection from "@/components/pages/home/<USER>";
import { cancelFrame, frame } from "framer-motion";
import ReactLenis, { LenisRef } from "lenis/react";
import { useEffect, useRef } from "react";

export default function Home() {
   const lenisRef = useRef<LenisRef>(null);

   useEffect(() => {
      function update(data: { timestamp: number }) {
         const time = data.timestamp;
         lenisRef.current?.lenis?.raf(time);
      }

      frame.update(update, true);

      return () => cancelFrame(update);
   }, []);

   return (
      <>
         <ReactLenis root ref={lenisRef}>
            <HeroSection />
            <div className="bg-background relative z-50 mt-[calc(100vh-80px)]">
               <IntroSection />
               <AboutSection />
               <ValueSection />
               <ServicesSection />
               <BrandsSection />
               <CtaSection />
            </div>
         </ReactLenis>
      </>
   );
}
