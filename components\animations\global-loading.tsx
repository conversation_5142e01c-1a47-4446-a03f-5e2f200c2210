"use client";

import { motion, useAnimation } from "motion/react";
import { useCallback, useEffect, useState } from "react";

interface GlobalLoadingProps {
   onComplete: () => void;
}

export default function GlobalLoading({ onComplete }: GlobalLoadingProps) {
   const [percentage, setPercentage] = useState(0);
   const [isExiting, setIsExiting] = useState(false);
   const progressBarControls = useAnimation();
   const percentageControls = useAnimation();
   const loadingTextControls = useAnimation();
   const logoControls = useAnimation();
   const containerControls = useAnimation();
   const progressBarContainerControls = useAnimation();

   const startExitAnimations = useCallback(async () => {
      // Start all exit animations simultaneously
      const exitPromises = [
         // Percentage slides out to the right (slower)
         percentageControls.start({
            x: "100vw",
            transition: {
               duration: 1.2,
               ease: [0.25, 0.46, 0.45, 0.94],
            },
         }),

         // Loading text slides out to the bottom (slower)
         loadingTextControls.start({
            y: "100vh",
            transition: {
               duration: 1.2,
               ease: [0.25, 0.46, 0.45, 0.94],
            },
         }),

         // Progress bar slides out to the top (slower)
         progressBarContainerControls.start({
            y: "-100vh",
            transition: {
               duration: 1.2,
               ease: [0.25, 0.46, 0.45, 0.94],
            },
         }),

         // Logo fades out
         logoControls.start({
            opacity: 0,
            transition: {
               duration: 0.8,
               ease: [0.25, 0.46, 0.45, 0.94],
            },
         }),
      ];

      // Wait for all exit animations to complete
      await Promise.all(exitPromises);

      // Add container opacity fade-out after all elements have exited
      await containerControls.start({
         opacity: 0,
         transition: {
            duration: 0.3,
            ease: [0.25, 0.46, 0.45, 0.94],
         },
      });

      // Minimal delay before triggering hero section (100ms total)
      setTimeout(() => {
         onComplete();
      }, 100);
   }, [
      percentageControls,
      loadingTextControls,
      progressBarContainerControls,
      logoControls,
      containerControls,
      onComplete,
   ]);

   useEffect(() => {
      // Sequence entrance animations before loading progression
      const startSequence = async () => {
         // Start all entrance animations simultaneously
         const entrancePromises = [
            logoControls.start({
               opacity: 1,
               transition: {
                  duration: 0.6,
                  delay: 0.2,
                  ease: [0.25, 0.46, 0.45, 0.94],
               },
            }),
            progressBarContainerControls.start({
               y: 0,
               opacity: 1,
               transition: {
                  duration: 0.8,
                  delay: 0.3,
                  ease: [0.25, 0.46, 0.45, 0.94],
               },
            }),
            percentageControls.start({
               x: 0,
               opacity: 1,
               transition: {
                  duration: 0.8,
                  delay: 0.4,
                  ease: [0.25, 0.46, 0.45, 0.94],
               },
            }),
            loadingTextControls.start({
               y: 0,
               opacity: 1,
               transition: {
                  duration: 0.8,
                  delay: 0.6,
                  ease: [0.25, 0.46, 0.45, 0.94],
               },
            }),
         ];

         // Wait for all entrance animations to complete
         await Promise.all(entrancePromises);

         // Now start the loading progression
         await loadingSequence();
      };

      // Simulate realistic loading behavior with non-linear progression
      const loadingSequence = async () => {
         // Initial fast loading (0-40%)
         for (let i = 0; i <= 40; i += 3) {
            setPercentage(i);
            await new Promise((resolve) => setTimeout(resolve, 40));
         }

         // Medium speed loading (40-75%)
         for (let i = 40; i <= 75; i += 2) {
            setPercentage(i);
            await new Promise((resolve) => setTimeout(resolve, 60));
         }

         // Slower loading (75-95%)
         for (let i = 75; i <= 95; i += 1) {
            setPercentage(i);
            await new Promise((resolve) => setTimeout(resolve, 100));
         }

         // Final push to 100%
         for (let i = 95; i <= 100; i += 1) {
            setPercentage(i);
            await new Promise((resolve) => setTimeout(resolve, 150));
         }

         // Hold at 100% briefly before exit
         await new Promise((resolve) => setTimeout(resolve, 300));

         // Start exit animations
         setIsExiting(true);
         await startExitAnimations();
      };

      startSequence();
   }, [
      percentageControls,
      loadingTextControls,
      logoControls,
      progressBarContainerControls,
      startExitAnimations,
   ]);

   return (
      <motion.div
         className="bg-background fixed inset-0 z-[9999]"
         initial={{ opacity: 1 }}
         animate={containerControls}
      >
         {/* Top-left corner: Beckizedek logo */}
         <motion.div
            className="absolute top-11 left-5 z-10"
            initial={{ y: -20, opacity: 0 }}
            animate={logoControls}
            transition={{
               duration: 0.6,
               delay: 0.2,
               ease: [0.25, 0.46, 0.45, 0.94],
            }}
         >
            <div className="text-primary font-serif text-2xl font-thin italic">
               Beckizedek
            </div>
         </motion.div>

         {/* Top edge: Progress bar */}
         <motion.div
            className="absolute top-0 right-0 left-0 h-3 bg-gray-200/30"
            initial={{ y: "-100vh", opacity: 0 }}
            animate={progressBarContainerControls}
         >
            <motion.div
               className="bg-primary h-full"
               initial={{ width: "0%" }}
               animate={
                  isExiting
                     ? progressBarControls
                     : {
                          width: `${percentage}%`,
                          transition: {
                             duration: 0.15,
                             ease: "easeOut",
                          },
                       }
               }
            />
         </motion.div>

         {/* Top-right corner: Percentage counter */}
         <motion.div
            className="absolute top-8 right-6 z-10 lg:top-10 lg:right-8"
            initial={{ x: "100vw", opacity: 0 }}
            animate={percentageControls}
            transition={{
               duration: 0.8,
               delay: 0.4,
               ease: [0.25, 0.46, 0.45, 0.94],
            }}
         >
            <div className="overflow-hidden">
               <motion.div
                  className="text-primary font-anton text-8xl font-thin tracking-wider md:text-9xl lg:text-[11rem]"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{
                     duration: 0.2,
                     ease: [0.25, 0.46, 0.45, 0.94],
                  }}
               >
                  {percentage.toString()}%
               </motion.div>
            </div>
         </motion.div>

         {/* Bottom-left corner: Loading text */}
         <motion.div
            className="absolute bottom-5 left-5 z-10"
            initial={{ y: "100vh", opacity: 0 }}
            animate={loadingTextControls}
            transition={{
               duration: 0.8,
               delay: 0.6,
               ease: [0.25, 0.46, 0.45, 0.94],
            }}
         >
            <div className="font-anton text-primary text-5xl font-thin tracking-wider uppercase md:text-8xl lg:text-[10rem]">
               Loading
            </div>
         </motion.div>
      </motion.div>
   );
}
