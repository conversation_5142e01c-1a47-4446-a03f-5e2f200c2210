"use client";

import { motion, useScroll, useTransform } from "motion/react";
import Image from "next/image";
import { useRef } from "react";

interface Logo {
   src: string;
   alt: string;
   className?: string;
}

interface InfiniteLogoScrollProps {
   logos: Logo[];
   speed?: number;
   direction?: "left" | "right";
}

export default function InfiniteLogoScroll({
   logos,
   direction = "left",
}: InfiniteLogoScrollProps) {
   const containerRef = useRef<HTMLDivElement>(null);

   // Get scroll progress
   const { scrollYProgress } = useScroll({
      target: containerRef,
      offset: ["start end", "end start"],
   });

   // Transform scroll progress to horizontal movement
   const x = useTransform(
      scrollYProgress,
      [0, 1],
      direction === "left" ? ["0%", "-5%"] : ["-5%", "0%"],
   );

   // Duplicate logos for seamless infinite scroll
   const duplicatedLogos = [...logos, ...logos, ...logos];

   return (
      <div ref={containerRef} className="overflow-hidden pb-4 sm:pb-6 md:pb-8">
         <motion.div
            className="flex w-fit gap-4 sm:gap-6 md:gap-8"
            style={{ x }}
         >
            {duplicatedLogos.map((logo, index) => (
               <div
                  key={`${logo.alt}-${index}`}
                  className="flex h-22 w-34 flex-shrink-0 items-center justify-center rounded-xl bg-white shadow-sm md:h-24 md:w-42 lg:h-32 lg:w-68"
               >
                  <Image
                     src={logo.src}
                     alt={logo.alt}
                     width={80}
                     height={80}
                     className={`mx-auto mix-blend-multiply grayscale transition-all duration-300 hover:grayscale-0 ${
                        logo.className || ""
                     }`}
                  />
               </div>
            ))}
         </motion.div>
      </div>
   );
}
