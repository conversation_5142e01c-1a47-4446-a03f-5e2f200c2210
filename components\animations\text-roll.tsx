"use client";

import { cn } from "@/lib/utils";
import { motion } from "motion/react";

const STAGGER = 0.035;

type Props = {
   children: React.ReactNode;
   className?: string;
   center?: boolean;
};

export default function TextRoll({
   children,
   className,
   center = false,
}: Props) {
   if (!children) return null;

   return (
      <motion.span
         initial="initial"
         whileHover="hovered"
         className={cn("relative block overflow-hidden", className)}
         // style={{
         //    lineHeight: 0.75,
         // }}
      >
         <div>
            {children
               .toString()
               .split("")
               .map((l: string, i: number) => {
                  const delay = center
                     ? STAGGER *
                       Math.abs(i - (children.toString().length - 1) / 2)
                     : STAGGER * i;

                  if (l === " ")
                     return (
                        <span
                           key={i}
                           className="inline-block w-1"
                           aria-hidden="true"
                        />
                     );

                  return (
                     <motion.span
                        variants={{
                           initial: {
                              y: 0,
                           },
                           hovered: {
                              y: "-100%",
                           },
                        }}
                        transition={{
                           ease: "easeInOut",
                           delay,
                        }}
                        className="inline-block"
                        key={i}
                     >
                        {l}
                     </motion.span>
                  );
               })}
         </div>
         <div className="absolute inset-0">
            {children
               .toString()
               .split("")
               .map((l: string, i: number) => {
                  const delay = center
                     ? STAGGER *
                       Math.abs(i - (children.toString().length - 1) / 2)
                     : STAGGER * i;

                  if (l === " ")
                     return (
                        <span
                           key={i}
                           className="inline-block w-1"
                           aria-hidden="true"
                        />
                     );

                  return (
                     <motion.span
                        variants={{
                           initial: {
                              y: "100%",
                           },
                           hovered: {
                              y: 0,
                           },
                        }}
                        transition={{
                           ease: "easeInOut",
                           delay,
                        }}
                        className="inline-block"
                        key={i}
                     >
                        {l}
                     </motion.span>
                  );
               })}
         </div>
      </motion.span>
   );
}
